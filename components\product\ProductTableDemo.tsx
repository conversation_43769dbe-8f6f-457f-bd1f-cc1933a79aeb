"use client"

import * as React from "react"
import { ProductTable, Product } from "./ProductTable"

// Sample data matching the image description
const SAMPLE_PRODUCTS: Product[] = [
  {
    id: "1",
    name: "Chicken Breast",
    calories: 165,
    protein: 31,
    carbs: 0,
    fat: 3.6,
    isUserProduct: true,
  },
  {
    id: "2",
    name: "<PERSON> Rice",
    calories: 111,
    protein: 2.6,
    carbs: 23,
    fat: 0.9,
    isUserProduct: false,
  },
  {
    id: "3",
    name: "Greek Yogurt",
    calories: 59,
    protein: 10,
    carbs: 3.6,
    fat: 0.4,
    isUserProduct: true,
  },
  {
    id: "4",
    name: "Almonds",
    calories: 579,
    protein: 21,
    carbs: 22,
    fat: 50,
    isUserProduct: false,
  },
  {
    id: "5",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    calories: 34,
    protein: 2.8,
    carbs: 7,
    fat: 0.4,
    isUserProduct: true,
  },
]

export function ProductTableDemo() {
  const [products, setProducts] = React.useState<Product[]>(SAMPLE_PRODUCTS)

  const handleEdit = (product: Product) => {
    console.log("Edit product:", product)
    // TODO: Open edit modal or navigate to edit page
    alert(`Edit product: ${product.name}`)
  }

  const handleDelete = (productId: string) => {
    const product = products.find(p => p.id === productId)
    if (product && window.confirm(`Are you sure you want to delete "${product.name}"?`)) {
      setProducts(prev => prev.filter(p => p.id !== productId))
      console.log("Deleted product:", productId)
    }
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Product Table Demo</h1>
      
      <div className="mb-4 p-3 bg-gray-50 rounded-md text-sm">
        <strong>Features:</strong>
        <ul className="mt-1 space-y-1 list-disc list-inside">
          <li>Displays product information in a structured table</li>
          <li>Shows nutritional information per 100g</li>
          <li>Indicates shared products with subtle text</li>
          <li>Edit and delete actions for each product</li>
          <li>Responsive design with proper spacing</li>
          <li>Empty state when no products are found</li>
        </ul>
      </div>

      <ProductTable
        products={products}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {products.length === 0 && (
        <div className="mt-4 text-center">
          <p className="text-muted-foreground">All products have been deleted.</p>
          <button
            onClick={() => setProducts(SAMPLE_PRODUCTS)}
            className="mt-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Restore Sample Data
          </button>
        </div>
      )}
    </div>
  )
}
